import { NextResponse, NextRequest } from "next/server";
import { unstable_noStore } from "next/cache";
import { PropertyBusiness } from "../business";
import { JC_Utils_Business } from "@/app/Utils";
import { PropertyModel } from "@/app/models/Property";

export const dynamic = 'force-dynamic';

// Get all Property
export async function GET(request: NextRequest) {
    try {
        unstable_noStore();

        const { searchParams } = new URL(request.url);
        const paging = JC_Utils_Business.getPagingFromParams(searchParams, PropertyModel);
        let result = await JC_Utils_Business.sqlGetList<PropertyModel>(
            PropertyModel,
            undefined,
            paging
        );

        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
