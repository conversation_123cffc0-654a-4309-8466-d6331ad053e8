import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { ReportModel } from "@/app/models/Report";
import { JC_Utils_Business } from "@/app/Utils";

export const dynamic = 'force-dynamic';

// Get all Report, optionally filtered by userId
export async function GET(request: NextRequest) {
    try {
        unstable_noStore();
        const params = new URL(request.url).searchParams;
        const userId = params.get("userId");

        let whereClause = userId ? `"UserId" = '${userId}'` : undefined;

        const result = await JC_Utils_Business.sqlGetList(ReportModel, whereClause, {
            PageSize: undefined,
            PageIndex: undefined,
            SortField: "CreatedAt",
            SortAsc: false
        });

        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
