import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { O_RoofModel } from "@/app/models/O_Roof";
import { O_RoofBusiness } from "./business";
import { JC_Utils_Business } from "@/app/Utils";

export const dynamic = 'force-dynamic';

// ------- //
// - GET - //
// ------- //

// Get by "Code"
export async function GET(request: NextRequest) {
    try {
        unstable_noStore();
        const params = new URL(request.url).searchParams;
        const code = params.get("code");

        if (!code) {
            return NextResponse.json({ error: "Missing 'code' parameter" }, { status: 400 });
        }

        let result = await JC_Utils_Business.sqlGet(O_RoofModel, code);
        return NextResponse.json({ result }, { status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// ---------- //
// - CREATE - //
// ---------- //

export async function PUT(request: NextRequest) {
    try {
        const params = new URL(request.url).searchParams;
        const isList = params.get("list") === "true";

        if (isList) {
            const dataList: O_RoofModel[] = await request.json();
            await O_RoofBusiness.CreateList(dataList);
        } else {
            const data: O_RoofModel = await request.json();
            await O_RoofBusiness.Create(data);
        }

        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// ---------- //
// - UPDATE - //
// ---------- //

export async function POST(request: NextRequest) {
    try {
        const params = new URL(request.url).searchParams;
        const sortOrderOnly = params.get("sortOrder") === "true";

        if (sortOrderOnly) {
            const { code, sortOrder } = await request.json();
            await O_RoofBusiness.UpdateSortOrder(code, sortOrder);
        } else {
            const data: O_RoofModel = await request.json();
            await O_RoofBusiness.Update(data);
        }

        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// ---------- //
// - DELETE - //
// ---------- //

export async function DELETE(request: NextRequest) {
    try {
        const params = new URL(request.url).searchParams;
        const code = params.get("code");
        const codes = params.get("codes");

        if (codes) {
            const codeList: string[] = codes.split(',');
            await O_RoofBusiness.DeleteList(codeList);
        } else if (code) {
            await O_RoofBusiness.Delete(code);
        } else {
            return NextResponse.json({ error: "Missing 'code' or 'codes' parameter" }, { status: 400 });
        }

        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
