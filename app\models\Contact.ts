import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetList } from "../apiServices/JC_GetList";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_Delete } from "../apiServices/JC_Delete";
import { JC_Utils } from "../Utils";
import { _Base } from "./_Base";

export class ContactModel extends _Base {

    static tableName: string = "Contact";
    static apiRoute: string = this.tableName.toLowerCase();
    static primaryKey: string = "Id";
    static defaultSortField: string = "Name";

    // - -------- - //
    // - SERVICES - //
    // - -------- - //
    static async Get(id: string) {
        return await JC_Get<ContactModel>(this.apiRoute, { id }, ContactModel);
    }
    static async GetList() {
        return await JC_GetList<ContactModel>(`${this.apiRoute}/getList`, ContactModel);
    }
    static async Create(data: ContactModel) {
        return await JC_Put<ContactModel>(this.apiRoute, data);
    }
    static async CreateList(dataList: ContactModel[]) {
        return await JC_Put<ContactModel[]>(`${this.apiRoute}/createList`, dataList);
    }
    static async Update(data: ContactModel) {
        return await JC_Post<ContactModel>(this.apiRoute, data);
    }
    static async UpdateList(dataList: ContactModel[]) {
        return await JC_Post<ContactModel[]>(`${this.apiRoute}/updateList`, dataList);
    }
    static async Delete(id: string) {
        return await JC_Delete(this.apiRoute, id);
    }
    static async DeleteList(ids: string[]) {
        return await JC_Post(`${this.apiRoute}/deleteList`, { ids });
    }

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    UserId?: string;
    Name: string;
    Email: string;
    Phone?: string;
    Message: string;

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<ContactModel>) {
        super(init);
        this.Id = JC_Utils.generateGuid();
        this.UserId = undefined;
        this.Name = "";
        this.Email = "";
        this.Phone = undefined;
        this.Message = "";
        Object.assign(this, init);
    }

    static getKeys() {
        return Object.keys(new ContactModel());
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `${this.Name} | ${this.Email} | ${this.Message}`;
    }
}
