import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { ContactModel } from "@/app/models/Contact";
import { ContactBusiness } from "./business";
import { JC_Utils_Business } from "@/app/Utils";

export const dynamic = 'force-dynamic';

// ------- //
// - GET - //
// ------- //

// Get by "Id"
export async function GET(request: NextRequest) {
    try {
        unstable_noStore();
        const params = new URL(request.url).searchParams;
        const id = params.get("id");

        if (!id) {
            return NextResponse.json({ error: "Missing 'id' parameter" }, { status: 400 });
        }

        let result = await JC_Utils_Business.sqlGet(ContactModel, id);
        return NextResponse.json({ result }, { status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// ---------- //
// - CREATE - //
// ---------- //

export async function PUT(request: NextRequest) {
    try {
        const params = new URL(request.url).searchParams;
        const isList = params.get("list") === "true";

        if (isList) {
            const dataList: ContactModel[] = await request.json();
            await ContactBusiness.CreateList(dataList);
        } else {
            const data: ContactModel = await request.json();
            await ContactBusiness.Create(data);
        }

        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// ---------- //
// - UPDATE - //
// ---------- //

export async function POST(request: NextRequest) {
    try {
        const params = new URL(request.url).searchParams;
        const isList = params.get("list") === "true";

        if (isList) {
            const dataList: ContactModel[] = await request.json();
            await ContactBusiness.UpdateList(dataList);
        } else {
            const data: ContactModel = await request.json();
            await ContactBusiness.Update(data);
        }

        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// ---------- //
// - DELETE - //
// ---------- //

export async function DELETE(request: NextRequest) {
    try {
        const params = new URL(request.url).searchParams;
        const id = params.get("id");
        const ids = params.get("ids");

        if (ids) {
            const idList: string[] = ids.split(',');
            await ContactBusiness.DeleteList(idList);
        } else if (id) {
            await ContactBusiness.Delete(id);
        } else {
            return NextResponse.json({ error: "Missing 'id' or 'ids' parameter" }, { status: 400 });
        }

        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}