import { NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { PropertyDefectModel } from "@/app/models/PropertyDefect";
import { JC_Utils_Business } from "@/app/Utils";

export const dynamic = 'force-dynamic';

// Get all PropertyDefect
export async function GET() {
    try {
        unstable_noStore();
        const result = await JC_Utils_Business.sqlGetList(PropertyDefectModel, undefined, {
            PageSize: undefined,
            PageIndex: undefined,
            SortField: "Id",
            SortAsc: true
        });
        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
