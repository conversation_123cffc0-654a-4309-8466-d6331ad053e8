import { NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { O_CompanyStrataTitleModel } from "@/app/models/O_CompanyStrataTitle";
import { JC_Utils_Business } from "@/app/Utils";

export const dynamic = 'force-dynamic';

// Get all O_CompanyStrataTitle
export async function GET() {
    try {
        unstable_noStore();
        const result = await JC_Utils_Business.sqlGetList(O_CompanyStrataTitleModel, undefined, {
            PageSize: undefined,
            PageIndex: undefined,
            SortField: "SortOrder",
            SortAsc: true
        });
        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
