import { NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { UserModel } from "@/app/models/User";
import { JC_Utils_Business } from "@/app/Utils";

export const dynamic = 'force-dynamic';

// Get all User
export async function GET() {
    try {
        unstable_noStore();
        const result = await JC_Utils_Business.sqlGetList(UserModel, undefined, {
            PageSize: undefined,
            PageIndex: undefined,
            SortField: "FirstName",
            SortAsc: true
        });
        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
