import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetList } from "../apiServices/JC_GetList";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_Delete } from "../apiServices/JC_Delete";
import { _Base } from "./_Base";
import { JC_ListPagingModel } from "./ComponentModels/JC_ListPagingModel";

export class O_FurnishedModel extends _Base {

    static tableName: string = "O_Furnished";
    static apiRoute: string = this.tableName.toLowerCase();
    static primaryKey: string = "Code";
    static defaultSortField: string = "Name";

    // - -------- - //
    // - SERVICES - //
    // - -------- - //
    static async Get(code: string) {
        return await JC_Get<O_FurnishedModel>(this.apiRoute, { code }, O_FurnishedModel);
    }
    static async GetList(paging?:JC_ListPagingModel) {
        return await JC_GetList<O_FurnishedModel>(`${this.apiRoute}/getList`, O_FurnishedModel, paging, {});
    }
    static async Create(data: O_FurnishedModel) {
        return await JC_Put<O_FurnishedModel>(this.apiRoute, data);
    }
    static async CreateList(dataList: O_FurnishedModel[]) {
        return await JC_Put<O_FurnishedModel[]>(`${this.apiRoute}/createList`, dataList);
    }
    static async Update(data: O_FurnishedModel) {
        return await JC_Post<O_FurnishedModel>(this.apiRoute, data);
    }
    static async UpdateList(dataList: O_FurnishedModel[]) {
        return await JC_Post<O_FurnishedModel[]>(`${this.apiRoute}/updateList`, dataList);
    }
    static async Delete(code: string) {
        return await JC_Delete(this.apiRoute, code);
    }
    static async DeleteList(codes: string[]) {
        return await JC_Post(`${this.apiRoute}/deleteList`, { codes });
    }

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Code: string;
    Name: string;
    SortOrder: number;

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<O_FurnishedModel>) {
        super(init);
        this.Code = "";
        this.Name = "";
        this.SortOrder = 999;
        Object.assign(this, init);
    }

    static getKeys() {
        return Object.keys(new O_FurnishedModel());
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `${this.Code} | ${this.Name}`;
    }
}
